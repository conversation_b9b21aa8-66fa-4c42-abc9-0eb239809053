<!-- templates/dashboard/add_servico.html -->
{% extends "base.html" %}

{% block title %}<PERSON><PERSON><PERSON>r <PERSON> - Oficina EPG{% endblock %}

{% block extra_head %}
<style>
    .form-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
        overflow: hidden;
        position: relative;
    }

    .form-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        border: 2px solid rgba(102, 126, 234, 0.1);
        border-radius: 12px;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.8);
    }

    .form-control:focus {
        border-color: var(--primary-gradient);
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }

    .btn-submit {
        background: var(--primary-gradient);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        width: 100%;
    }

    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-back {
        background: transparent;
        border: 2px solid var(--border-color);
        color: var(--text-primary);
        padding: 0.5rem 1.5rem;
        border-radius: 12px;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-back:hover {
        border-color: var(--text-primary);
        background: var(--text-primary);
        color: white;
        transform: translateY(-2px);
    }

    .section-title {
        font-family: var(--font-heading);
        font-size: 1.75rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-title i {
        color: var(--primary-gradient);
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .help-text {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-top: 0.25rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="section-title">
                <i class="fas fa-plus-circle"></i>
                Adicionar Novo Serviço
            </h1>
            <p class="text-muted mb-0">Preencha os dados do novo serviço para adicionar ao sistema</p>
        </div>
        <a href="{% url 'dashboard_home' %}" class="btn-back">
            <i class="fas fa-arrow-left"></i>
            Voltar ao Dashboard
        </a>
    </div>

    <!-- Form Section -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-card">
                <div class="card-body p-4">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}

                        <div class="form-group">
                            <label for="{{ form.nome.id_for_label }}" class="form-label">
                                <i class="fas fa-tag me-2"></i>Nome do Serviço
                            </label>
                            {{ form.nome }}
                            <div class="help-text">Digite o nome do serviço (máximo 100 caracteres)</div>
                            {% if form.nome.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.nome.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.descricao.id_for_label }}" class="form-label">
                                <i class="fas fa-align-left me-2"></i>Descrição
                            </label>
                            {{ form.descricao }}
                            <div class="help-text">Descreva detalhadamente o serviço oferecido (opcional)</div>
                            {% if form.descricao.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.descricao.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.imagem_url.id_for_label }}" class="form-label">
                                <i class="fas fa-image me-2"></i>URL da Imagem
                            </label>
                            {{ form.imagem_url }}
                            <div class="help-text">Cole o link da imagem do serviço (opcional)</div>
                            {% if form.imagem_url.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.imagem_url.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.preco.id_for_label }}" class="form-label">
                                <i class="fas fa-euro-sign me-2"></i>Preço
                            </label>
                            {{ form.preco }}
                            <div class="help-text">Preço do serviço em euros (ex: 25.50)</div>
                            {% if form.preco.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.preco.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn-submit">
                                <i class="fas fa-save me-2"></i>
                                Adicionar Serviço
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Add Bootstrap classes to form fields
    document.addEventListener('DOMContentLoaded', function() {
        const formFields = document.querySelectorAll('input, textarea, select');
        formFields.forEach(field => {
            field.classList.add('form-control');
            if (field.type === 'textarea') {
                field.rows = 4;
            }
        });
    });
</script>
{% endblock %}