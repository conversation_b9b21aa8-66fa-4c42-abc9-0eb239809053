{% load tz %}

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Oficina EPG{% endblock %}</title>

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts for premium typography -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Space+Grotesk:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">
    
    <style>
        :root {
            /* Design System Variables */
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --danger-gradient: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
            
            /* Glass morphism variables */
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            
            /* Spacing system */
            --nav-height: 80px;
            --container-padding: 2rem;
            
            /* Typography */
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-heading: 'Space Grotesk', -apple-system, BlinkMacSystemFont, sans-serif;
            
            /* Colors */
            --text-primary: #1a202c;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --text-white: #ffffff;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --border-color: #e2e8f0;
            
            /* Dark mode variables */
            --dark-bg-primary: #0f172a;
            --dark-bg-secondary: #1e293b;
            --dark-text-primary: #f1f5f9;
            --dark-text-secondary: #cbd5e1;
            --dark-border-color: #334155;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-primary);
            font-weight: 400;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-primary);
            overflow-x: hidden;
            transition: all 0.3s ease;
        }

        /* Navigation */
        .navbar-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            padding: 0;
            transition: all 0.3s ease;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1050;
            height: var(--nav-height);
        }

        .navbar-custom.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: var(--glass-shadow);
        }

        .navbar-custom .container {
            height: var(--nav-height);
            display: flex;
            align-items: center;
        }

        .navbar-brand-custom {
            font-family: var(--font-heading);
            font-size: 1.75rem;
            font-weight: 700;
            text-decoration: none;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            transition: all 0.3s ease;
            margin-right: 2rem;
        }

        .navbar-brand-custom:hover {
            transform: translateY(-2px);
            filter: brightness(1.1);
        }

        .navbar-nav-custom {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-item-custom {
            position: relative;
        }

        .nav-link-custom {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.25rem;
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.95rem;
            border-radius: 12px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-link-custom::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--accent-gradient);
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 12px;
        }

        .nav-link-custom:hover::before {
            opacity: 0.1;
        }

        .nav-link-custom:hover {
            color: var(--text-primary);
            transform: translateY(-2px);
        }

        .nav-link-custom i {
            margin-right: 0.5rem;
            font-size: 0.9rem;
        }

        /* Dropdown styling */
        .dropdown-custom {
            position: relative;
        }

        .dropdown-toggle-custom {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .dropdown-toggle-custom::after {
            content: '\f107';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            border: none;
            margin-left: 0.5rem;
            transition: transform 0.3s ease;
        }

        .dropdown-custom.show .dropdown-toggle-custom::after {
            transform: rotate(180deg);
        }

        .dropdown-menu-custom {
            position: absolute;
            top: 100%;
            left: 0;
            min-width: 220px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            box-shadow: var(--glass-shadow);
            padding: 0.75rem 0;
            margin-top: 0.5rem;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .dropdown-custom.show .dropdown-menu-custom {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item-custom {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.25rem;
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            border-radius: 0;
        }

        .dropdown-item-custom:hover {
            background: var(--glass-bg);
            color: var(--text-primary);
            padding-left: 1.5rem;
        }

        .dropdown-item-custom i {
            margin-right: 0.75rem;
            width: 16px;
            text-align: center;
            opacity: 0.7;
        }

        /* CTA Buttons in Navigation */
        .btn-nav-primary {
            background: var(--primary-gradient);
            border: none;
            color: white;
            padding: 0.6rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.9rem;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            margin-left: 0.5rem;
        }

        .btn-nav-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .btn-nav-secondary {
            background: transparent;
            border: 2px solid var(--border-color);
            color: var(--text-primary);
            padding: 0.5rem 1.25rem;
            border-radius: 50px;
            font-weight: 500;
            font-size: 0.9rem;
            text-decoration: none;
            transition: all 0.3s ease;
            margin-left: 0.5rem;
        }

        .btn-nav-secondary:hover {
            border-color: var(--text-primary);
            background: var(--text-primary);
            color: white;
            transform: translateY(-2px);
        }

        /* Mobile Navigation */
        .navbar-toggler-custom {
            background: none;
            border: none;
            padding: 0.5rem;
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .navbar-toggler-custom span {
            display: block;
            width: 24px;
            height: 2px;
            background: var(--text-primary);
            margin: 3px 0;
            transition: all 0.3s ease;
            border-radius: 2px;
        }

        .navbar-toggler-custom.active span:nth-child(1) {
            transform: rotate(45deg) translate(6px, 6px);
        }

        .navbar-toggler-custom.active span:nth-child(2) {
            opacity: 0;
        }

        .navbar-toggler-custom.active span:nth-child(3) {
            transform: rotate(-45deg) translate(6px, -6px);
        }

        /* Main Content Adjustment */
        .main-content {
            margin-top: var(--nav-height);
            min-height: calc(100vh - var(--nav-height));
        }

        /* Footer */
        .footer-custom {
            background: var(--dark-bg-primary);
            color: var(--dark-text-primary);
            padding: 3rem 0 1.5rem;
            margin-top: auto;
            position: relative;
            overflow: hidden;
        }

        .footer-custom::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--primary-gradient);
        }

        .footer-content {
            text-align: center;
        }

        .footer-brand {
            font-family: var(--font-heading);
            font-size: 1.5rem;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .footer-text {
            color: var(--dark-text-secondary);
            font-size: 0.95rem;
            margin-bottom: 0;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin: 1.5rem 0;
            flex-wrap: wrap;
        }

        .footer-link {
            color: var(--dark-text-secondary);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .footer-link:hover {
            color: var(--dark-text-primary);
            transform: translateY(-2px);
        }

        /* Alert Styling */
        .alert-custom {
            border: none;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .alert-success-custom {
            background: linear-gradient(135deg, rgba(17, 153, 142, 0.1), rgba(56, 239, 125, 0.1));
            color: #065f46;
            border-left: 4px solid #10b981;
        }

        .alert-danger-custom {
            background: linear-gradient(135deg, rgba(252, 70, 107, 0.1), rgba(63, 94, 251, 0.1));
            color: #991b1b;
            border-left: 4px solid #ef4444;
        }

        .alert-warning-custom {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(251, 191, 36, 0.1));
            color: #92400e;
            border-left: 4px solid #f59e0b;
        }

        .alert-info-custom {
            background: linear-gradient(135deg, rgba(79, 172, 254, 0.1), rgba(0, 242, 254, 0.1));
            color: #1e40af;
            border-left: 4px solid #3b82f6;
        }

        /* Responsive Design */
        @media (max-width: 991px) {
            .navbar-toggler-custom {
                display: flex;
            }

            .navbar-nav-custom {
                position: fixed;
                top: var(--nav-height);
                left: 0;
                right: 0;
                background: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(20px);
                flex-direction: column;
                padding: 2rem;
                transform: translateY(-100%);
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                box-shadow: var(--glass-shadow);
                gap: 0;
            }

            .navbar-nav-custom.show {
                transform: translateY(0);
                opacity: 1;
                visibility: visible;
            }

            .nav-item-custom {
                width: 100%;
                margin-bottom: 0.5rem;
            }

            .nav-link-custom {
                justify-content: flex-start;
                width: 100%;
                padding: 1rem;
            }

            .dropdown-menu-custom {
                position: static;
                opacity: 1;
                visibility: visible;
                transform: none;
                background: transparent;
                border: none;
                box-shadow: none;
                margin-left: 1rem;
                margin-top: 0.5rem;
                backdrop-filter: none;
            }

            .btn-nav-primary,
            .btn-nav-secondary {
                margin: 1rem 0 0 0;
                width: 100%;
                text-align: center;
            }
        }

        @media (max-width: 576px) {
            :root {
                --nav-height: 70px;
                --container-padding: 1rem;
            }

            .footer-links {
                flex-direction: column;
                gap: 1rem;
            }
        }

        /* Loading animation for better UX */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 1;
            visibility: visible;
            transition: all 0.5s ease;
        }

        .loading-overlay.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid var(--border-color);
            border-top: 3px solid transparent;
            border-radius: 50%;
            background: var(--primary-gradient);
            -webkit-mask: radial-gradient(farthest-side, transparent calc(100% - 3px), #000 0);
            mask: radial-gradient(farthest-side, transparent calc(100% - 3px), #000 0);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Accessibility improvements */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Focus states for accessibility */
        .nav-link-custom:focus,
        .dropdown-item-custom:focus,
        .btn-nav-primary:focus,
        .btn-nav-secondary:focus {
            outline: 2px solid var(--primary-gradient);
            outline-offset: 2px;
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Navbar -->
    <nav class="navbar-custom" id="mainNavbar">
        <div class="container">
            <a class="navbar-brand-custom" href="/">
                <i class="fas fa-cog me-2"></i>Oficina EPG
            </a>
            
            <button class="navbar-toggler-custom" type="button" id="navbarToggler" aria-label="Toggle navigation">
                <span></span>
                <span></span>
                <span></span>
            </button>

            <ul class="navbar-nav-custom ms-auto" id="navbarNav">
                <li class="nav-item-custom">
                    <a class="nav-link-custom" href="{% url 'landing' %}">
                        <i class="fas fa-home"></i>
                        Início
                    </a>
                </li>
                <li class="nav-item-custom">
                    <a class="nav-link-custom" href="{% url 'servicos_list' %}">
                        <i class="fas fa-cogs"></i>
                        Serviços
                    </a>
                </li>
                {% if user.is_authenticated %}
                    {% if user.role.nome == 'admin' %}
                        <!-- Admin Dropdown -->
                        <li class="nav-item-custom dropdown-custom" id="adminDropdown">
                            <a class="nav-link-custom dropdown-toggle-custom" href="#" role="button" aria-expanded="false">
                                <i class="fas fa-tachometer-alt"></i>
                                Admin
                            </a>
                            <ul class="dropdown-menu-custom">
                                <li><a class="dropdown-item-custom" href="{% url 'dashboard_home' %}"><i class="fas fa-home"></i> Dashboard</a></li>
                                <li><a class="dropdown-item-custom" href="{% url 'dashboard_precos' %}"><i class="fas fa-euro-sign"></i> Preços</a></li>
                                <li><a class="dropdown-item-custom" href="{% url 'dashboard_servicos' %}"><i class="fas fa-wrench"></i> Serviços</a></li>
                            </ul>
                        </li>
                    {% endif %}
                    <li class="nav-item-custom">
                        <a class="nav-link-custom" href="#" onclick="alert('Logout functionality to be implemented')">
                            <i class="fas fa-sign-out-alt"></i>
                            Sair
                        </a>
                    </li>
                {% else %}
                    <li class="nav-item-custom">
                        <a class="nav-link-custom" href="#" onclick="alert('Login functionality to be implemented')">
                            <i class="fas fa-sign-in-alt"></i>
                            Entrar
                        </a>
                    </li>
                {% endif %}
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Alert Messages -->
        {% if messages %}
            <div class="container mt-3">
                {% for message in messages %}
                    <div class="alert-custom alert-{{ message.tags }}-custom" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        {% block content %}
        {% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer-custom">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">Oficina EPG</div>
                
                <div class="footer-links">
                    <a href="#" class="footer-link">Sobre Nós</a>
                    <a href="#" class="footer-link">Serviços</a>
                    <a href="#" class="footer-link">Contacto</a>
                    <a href="#" class="footer-link">Termos</a>
                    <a href="#" class="footer-link">Privacidade</a>
                </div>
                
                <p class="footer-text">
                    © {{ now|date:"Y" }} Oficina EPG. Desenvolvido por Fábio Pinto, Henryson, João.
                </p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    
    <script>
        // Navigation functionality
        class NavigationController {
            constructor() {
                this.navbar = document.getElementById('mainNavbar');
                this.navbarToggler = document.getElementById('navbarToggler');
                this.navbarNav = document.getElementById('navbarNav');
                this.adminDropdown = document.getElementById('adminDropdown');
                this.loadingOverlay = document.getElementById('loadingOverlay');
                
                this.init();
            }

            init() {
                this.setupScrollHandler();
                this.setupMobileNavigation();
                this.setupDropdowns();
                this.setupLoadingOverlay();
                this.setupAccessibility();
            }

            setupScrollHandler() {
                let scrollTimeout;
                window.addEventListener('scroll', () => {
                    clearTimeout(scrollTimeout);
                    scrollTimeout = setTimeout(() => {
                        if (window.scrollY > 20) {
                            this.navbar.classList.add('scrolled');
                        } else {
                            this.navbar.classList.remove('scrolled');
                        }
                    }, 10);
                });
            }

            setupMobileNavigation() {
                this.navbarToggler.addEventListener('click', () => {
                    this.navbarToggler.classList.toggle('active');
                    this.navbarNav.classList.toggle('show');
                    
                    // Prevent body scroll when mobile menu is open
                    document.body.style.overflow = this.navbarNav.classList.contains('show') ? 'hidden' : '';
                });

                // Close mobile menu when clicking outside
                document.addEventListener('click', (e) => {
                    if (!this.navbar.contains(e.target) && this.navbarNav.classList.contains('show')) {
                        this.closeMobileNav();
                    }
                });

                // Close mobile menu on escape key
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && this.navbarNav.classList.contains('show')) {
                        this.closeMobileNav();
                    }
                });
            }

            closeMobileNav() {
                this.navbarToggler.classList.remove('active');
                this.navbarNav.classList.remove('show');
                document.body.style.overflow = '';
            }

            setupDropdowns() {
                if (this.adminDropdown) {
                    const dropdownToggle = this.adminDropdown.querySelector('.dropdown-toggle-custom');
                    const dropdownMenu = this.adminDropdown.querySelector('.dropdown-menu-custom');

                    dropdownToggle.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        // Close other dropdowns
                        document.querySelectorAll('.dropdown-custom.show').forEach(dropdown => {
                            if (dropdown !== this.adminDropdown) {
                                dropdown.classList.remove('show');
                            }
                        });
                        
                        this.adminDropdown.classList.toggle('show');
                    });

                    // Close dropdown when clicking outside
                    document.addEventListener('click', (e) => {
                        if (!this.adminDropdown.contains(e.target)) {
                            this.adminDropdown.classList.remove('show');
                        }
                    });
                }
            }

            setupLoadingOverlay() {
                // Hide loading overlay after page load
                window.addEventListener('load', () => {
                    setTimeout(() => {
                        this.loadingOverlay.classList.add('hidden');
                        setTimeout(() => {
                            this.loadingOverlay.style.display = 'none';
                        }, 500);
                    }, 300);
                });
            }

            setupAccessibility() {
                // Keyboard navigation
                document.addEventListener('keydown', (e) => {
                    // Arrow key navigation for dropdowns
                    if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                        const focusedDropdown = document.querySelector('.dropdown-custom.show');
                        if (focusedDropdown) {
                            e.preventDefault();
                            const items = focusedDropdown.querySelectorAll('.dropdown-item-custom');
                            const currentIndex = Array.from(items).findIndex(item => item === document.activeElement);
                            
                            let nextIndex;
                            if (e.key === 'ArrowDown') {
                                nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
                            } else {
                                nextIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
                            }
                            
                            items[nextIndex].focus();
                        }
                    }
                });
            }
        }

        // Initialize navigation when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new NavigationController();
        });

        // Smooth scroll for anchor links
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href^="#"]');
            if (link) {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    const offsetTop = targetElement.offsetTop - (document.getElementById('mainNavbar').offsetHeight + 20);
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            }
        });

        // Performance optimization: Debounced resize handler
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                // Handle any resize-specific logic here
                const navController = new NavigationController();
                if (window.innerWidth > 991 && document.getElementById('navbarNav').classList.contains('show')) {
                    navController.closeMobileNav();
                }
            }, 250);
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>