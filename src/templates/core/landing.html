{% extends "base.html" %}

{% block title %}Bem-vindo à Oficina EPG - Serviços Automóveis de Excelência{% endblock %}

{% block extra_head %}
<style>
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --dark-overlay: rgba(0, 0, 0, 0.6);
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    overflow-x: hidden;
}

.hero-section {
    min-height: 100vh;
    background: var(--primary-gradient);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.shape {
    position: absolute;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    animation: float-shapes 15s infinite ease-in-out;
}

.shape:nth-child(1) {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape:nth-child(2) {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 10%;
    animation-delay: -5s;
}

.shape:nth-child(3) {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: -10s;
}

.hero-content {
    text-align: center;
    color: white;
    z-index: 2;
    position: relative;
    max-width: 800px;
    padding: 2rem;
}

.hero-title {
    font-size: clamp(2.5rem, 8vw, 4.5rem);
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, #fff, #e0e7ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: slideInUp 1s ease-out;
}

.hero-subtitle {
    font-size: clamp(1.1rem, 3vw, 1.5rem);
    margin-bottom: 2rem;
    opacity: 0.9;
    animation: slideInUp 1s ease-out 0.2s both;
}

.cta-container {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    animation: slideInUp 1s ease-out 0.4s both;
}

.btn-primary-custom {
    background: var(--secondary-gradient);
    border: none;
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.btn-primary-custom::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.btn-primary-custom:hover::before {
    left: 100%;
}

.btn-primary-custom:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    color: white;
}

.btn-secondary-custom {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 2px solid var(--glass-border);
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-secondary-custom:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
    color: white;
}

.services-section {
    padding: 6rem 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
}

.section-title {
    text-align: center;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: #64748b;
    margin-bottom: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.service-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--accent-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--accent-gradient);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 2rem;
    color: white;
}

.service-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1a202c;
}

.service-description {
    color: #64748b;
    line-height: 1.6;
}

.stats-section {
    background: var(--primary-gradient);
    color: white;
    padding: 4rem 0;
    position: relative;
}

.stat-item {
    text-align: center;
    padding: 1rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    display: block;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

.cta-section {
    background: #1a202c;
    color: white;
    padding: 6rem 0;
    text-align: center;
    position: relative;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="70" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
    animation: twinkle 3s ease-in-out infinite;
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    animation: bounce 2s infinite;
    cursor: pointer;
    z-index: 2;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes float-shapes {
    0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); }
    33% { transform: translateY(-30px) translateX(30px) rotate(120deg); }
    66% { transform: translateY(20px) translateX(-20px) rotate(240deg); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

@keyframes twinkle {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 1; }
}

@media (max-width: 768px) {
    .hero-content {
        padding: 1rem;
    }
    
    .cta-container {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-primary-custom,
    .btn-secondary-custom {
        width: 100%;
        max-width: 300px;
    }
    
    .service-card {
        margin-bottom: 2rem;
    }
}

.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>
    
    <div class="hero-content">
        <h1 class="hero-title">Oficina EPG</h1>
        <p class="hero-subtitle">Transformamos o cuidado do seu automóvel numa experiência de excelência. Serviços profissionais, tecnologia avançada e confiança absoluta.</p>
        
        <div class="cta-container">
            <a href="#servicos" class="btn-primary-custom">
                Nossos Serviços
            </a>
        </div>
    </div>
    
    <div class="scroll-indicator">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
        </svg>
    </div>
</section>

<!-- Estatísticas -->
<section class="stats-section">
    <div class="container">
        <div class="row">
            <div class="col-md-3 col-6">
                <div class="stat-item fade-in">
                    <span class="stat-number" data-count="1500">0</span>
                    <span class="stat-label">Clientes Satisfeitos</span>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-item fade-in">
                    <span class="stat-number" data-count="15">0</span>
                    <span class="stat-label">Anos de Experiência</span>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-item fade-in">
                    <span class="stat-number" data-count="98">0</span>
                    <span class="stat-label">% Satisfação</span>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-item fade-in">
                    <span class="stat-number" data-count="24">0</span>
                    <span class="stat-label">Horas de Serviço</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Serviços -->
<section id="servicos" class="services-section">
    <div class="container">
        <h2 class="section-title fade-in">Nossos Serviços</h2>
        <p class="section-subtitle fade-in">Oferecemos uma gama completa de serviços especializados para manter o seu veículo em perfeitas condições</p>
        
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="service-card fade-in">
                    <div class="service-icon">
                        🔧
                    </div>
                    <h3 class="service-title">Manutenção Preventiva</h3>
                    <p class="service-description">Mantenha o seu veículo em óptimas condições com as nossas revisões programadas e serviços de manutenção preventiva especializados.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="service-card fade-in">
                    <div class="service-icon">
                        ⚡
                    </div>
                    <h3 class="service-title">Diagnóstico Avançado</h3>
                    <p class="service-description">Utilizamos equipamentos de diagnóstico de última geração para identificar e resolver problemas com precisão e eficiência.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="service-card fade-in">
                    <div class="service-icon">
                        🛠️
                    </div>
                    <h3 class="service-title">Reparações Especializadas</h3>
                    <p class="service-description">Desde pequenos ajustes a reparações complexas, a nossa equipa experiente garante trabalho de qualidade superior.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="service-card fade-in">
                    <div class="service-icon">
                        🚗
                    </div>
                    <h3 class="service-title">Inspeções Técnicas</h3>
                    <p class="service-description">Prepare o seu veículo para as inspeções obrigatórias com os nossos serviços de verificação e preparação técnica.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="service-card fade-in">
                    <div class="service-icon">
                        🏆
                    </div>
                    <h3 class="service-title">Serviço Premium</h3>
                    <p class="service-description">Experimente o nosso serviço premium com recolha e entrega do veículo, relatórios detalhados e garantia estendida.</p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="service-card fade-in">
                    <div class="service-icon">
                        📱
                    </div>
                    <h3 class="service-title">Acompanhamento Digital</h3>
                    <p class="service-description">Acompanhe o progresso dos seus serviços em tempo real através da nossa plataforma digital intuitiva.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Final -->
<section class="cta-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h2 class="display-5 mb-4 fade-in">Pronto para Começar?</h2>
                <p class="lead mb-5 fade-in">Junte-se aos milhares de clientes que confiam na Oficina EPG para cuidar dos seus veículos.</p>
                
                <div class="cta-container fade-in">
                        <a href="{% url 'dashboard_home' %}" class="btn-primary-custom">
                            Aceder ao Painel
                        </a>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
// Animação de contadores
function animateCounters() {
    const counters = document.querySelectorAll('[data-count]');
    
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-count'));
        const increment = target / 200;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                counter.textContent = target + (counter.textContent.includes('%') ? '%' : '');
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current) + (counter.textContent.includes('%') ? '%' : '');
            }
        }, 10);
    });
}

// Observador de interseção para animações
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('visible');
            
            // Animar contadores quando a seção de estatísticas ficar visível
            if (entry.target.closest('.stats-section')) {
                animateCounters();
            }
        }
    });
}, observerOptions);

// Observar todos os elementos com animação
document.addEventListener('DOMContentLoaded', () => {
    const fadeElements = document.querySelectorAll('.fade-in');
    fadeElements.forEach(el => observer.observe(el));
    
    // Scroll suave para links âncora
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Indicador de scroll
    const scrollIndicator = document.querySelector('.scroll-indicator');
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', () => {
            document.querySelector('.stats-section').scrollIntoView({
                behavior: 'smooth'
            });
        });
    }
});

// Parallax suave para elementos flutuantes
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const parallaxElements = document.querySelectorAll('.floating-shapes .shape');
    
    parallaxElements.forEach((element, index) => {
        const speed = 0.5 + (index * 0.1);
        element.style.transform = `translateY(${scrolled * speed}px)`;
    });
});
</script>
{% endblock %}