from django.shortcuts import render, redirect
from django.contrib.auth.forms import AuthenticationForm, UserCreationForm
from django.contrib.auth import login as auth_login
from django.core.paginator import Paginator
from dashboard.models import Servico


def landing_page(request):
    return render(request, 'core/landing.html')


def servicos_list(request):
    """Public page displaying all available services"""
    servicos = Servico.objects.all().order_by('nome')

    # Add pagination
    paginator = Paginator(servicos, 6)  # Show 6 services per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'total_servicos': servicos.count(),
    }
    return render(request, 'core/servicos_list.html', context)
