# dashboard/forms.py
from django import forms
from django.core.validators import URLValidator
from .models import Servico


class ServicoForm(forms.ModelForm):
    class Meta:
        model = Servico
        fields = ['nome', 'descricao', 'imagem_url', 'preco']
        widgets = {
            'nome': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Ex: Mudança de óleo',
                'maxlength': 100
            }),
            'descricao': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'Descreva detalhadamente o serviço oferecido...',
                'rows': 4
            }),
            'imagem_url': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'https://exemplo.com/imagem.jpg'
            }),
            'preco': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            })
        }
        labels = {
            'nome': 'Nome do Serviço',
            'descricao': 'Descrição',
            'imagem_url': 'URL da Imagem',
            'preco': 'Preço (€)'
        }
        help_texts = {
            'nome': 'Digite o nome do serviço (máximo 100 caracteres)',
            'descricao': 'Descrição detalhada do serviço (opcional)',
            'imagem_url': 'Link para imagem do serviço (opcional)',
            'preco': 'Preço do serviço em euros'
        }

    def clean_preco(self):
        preco = self.cleaned_data.get('preco')
        if preco is not None and preco < 0:
            raise forms.ValidationError("O preço não pode ser negativo.")
        if preco is not None and preco > 9999.99:
            raise forms.ValidationError("O preço não pode exceder €9999.99.")
        return preco

    def clean_nome(self):
        nome = self.cleaned_data.get('nome')
        if not nome:
            raise forms.ValidationError("O nome é obrigatório.")
        if len(nome.strip()) < 3:
            raise forms.ValidationError("O nome deve ter pelo menos 3 caracteres.")
        return nome.strip()

    def clean_imagem_url(self):
        imagem_url = self.cleaned_data.get('imagem_url')
        if imagem_url:
            # Validate URL format
            validator = URLValidator()
            try:
                validator(imagem_url)
            except forms.ValidationError:
                raise forms.ValidationError("Por favor, insira uma URL válida.")

            # Check if URL ends with image extension
            valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
            if not any(imagem_url.lower().endswith(ext) for ext in valid_extensions):
                raise forms.ValidationError(
                    "A URL deve apontar para uma imagem válida (.jpg, .jpeg, .png, .gif, .webp)."
                )
        return imagem_url

    def clean_descricao(self):
        descricao = self.cleaned_data.get('descricao')
        if descricao and len(descricao.strip()) > 1000:
            raise forms.ValidationError("A descrição não pode exceder 1000 caracteres.")
        return descricao.strip() if descricao else descricao