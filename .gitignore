# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Virtual environments
.env
.env/
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/

# C extensions
*.so

# Django specific
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
instance/
.webassets-cache

# Test / coverage / CI
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Static analysis tools
.ruff_cache/
.mypy_cache/
.dmypy.json
.pyre/
.pytype/

# Distribution / packaging
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
*.spec

# PyInstaller
*.manifest

# Cython
cython_debug/

# Documentation
docs/_build/
site/

# Jupyter
.ipynb_checkpoints/

.pyc

# Project tools
.pdm.toml
.pdm-python
.pdm-build/
.pypirc
.cursorignore
.cursorindexingignore

# Local project settings
.ropeproject/
.spyderproject/
.spyproject/
.pybuilder/
target/

# PEP 582 support
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py
